2025-08-21 10:05:52.660 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-21 10:07:22.081 | INFO     | 5a1908c1d51f48cf940de78b8dc9fbc6 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:22.083 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:22.084 | INFO     | fb22b786d841476e80b635c1cd137ba9 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:22.088 | INFO     | 5a1908c1d51f48cf940de78b8dc9fbc6 | 成功认证Java用户: pythontest
2025-08-21 10:07:22.089 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | 成功认证Java用户: pythontest
2025-08-21 10:07:22.089 | INFO     | fb22b786d841476e80b635c1cd137ba9 | 成功认证Java用户: pythontest
2025-08-21 10:07:22.129 | INFO     | fb22b786d841476e80b635c1cd137ba9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:22.130 | INFO     | fb22b786d841476e80b635c1cd137ba9 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-21 10:07:22.136 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:22.137 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:22.141 | INFO     | 5a1908c1d51f48cf940de78b8dc9fbc6 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-21 10:07:22.150 | INFO     | 5a1908c1d51f48cf940de78b8dc9fbc6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 81.411ms
2025-08-21 10:07:22.163 | INFO     | fb22b786d841476e80b635c1cd137ba9 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:22.165 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:22.167 | INFO     | fb22b786d841476e80b635c1cd137ba9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 82.407ms
2025-08-21 10:07:22.168 | INFO     | dae482dd47114d749d13cf0f86cb5d06 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 85.724ms
2025-08-21 10:07:25.297 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:25.298 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | 成功认证Java用户: pythontest
2025-08-21 10:07:25.321 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:25.322 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:25.336 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:25.338 | INFO     | 4eab12d317414aa0a5da979530bc8dcb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 40.646ms
2025-08-21 10:07:25.349 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:25.350 | INFO     | 644d279c3daf4488ab646a0e3337e161 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:25.351 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | 成功认证Java用户: pythontest
2025-08-21 10:07:25.352 | INFO     | 644d279c3daf4488ab646a0e3337e161 | 成功认证Java用户: pythontest
2025-08-21 10:07:25.359 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:25.360 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:25.364 | INFO     | 644d279c3daf4488ab646a0e3337e161 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:25.365 | INFO     | 644d279c3daf4488ab646a0e3337e161 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:25.396 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:25.407 | INFO     | 644d279c3daf4488ab646a0e3337e161 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:25.409 | INFO     | 4d6baa8f6c8141f28e08071d381f68fb | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=20&orderby=create_time&desc=true | 60.495ms
2025-08-21 10:07:25.411 | INFO     | 644d279c3daf4488ab646a0e3337e161 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 60.602ms
2025-08-21 10:07:29.077 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:29.078 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 成功认证Java用户: pythontest
2025-08-21 10:07:29.083 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:29.084 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:07:29.201 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents/2f908d7847f511f0919188f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-21 10:07:29.204 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 文档预览调试 - doc_id: 2f908d7847f511f0919188f4da8e1b91
2025-08-21 10:07:29.204 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | doc_name: 'HSE专业安全知识试题一.docx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-21 10:07:29.205 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-21 10:07:29.206 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-21 10:07:29.206 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 最终识别的文档类型: office
2025-08-21 10:07:29.207 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 检测到Office文档: HSE专业安全知识试题一.docx，返回原始文档URL用于vue-office预览
2025-08-21 10:07:29.208 | INFO     | bcf5c6bace0c4071a041e8d594086a39 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/2f908d7847f511f0919188f4da8e1b91/preview/doc_name=HSE%E4%B8%93%E4%B8%9A%E5%AE%89%E5%85%A8%E7%9F%A5%E8%AF%86%E8%AF%95%E9%A2%98%E4%B8%80.docx | 131.660ms
2025-08-21 10:07:29.216 | INFO     | 850bd3374a0442afa85e464f60da59d5 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:29.217 | INFO     | 850bd3374a0442afa85e464f60da59d5 | 成功认证Java用户: pythontest
2025-08-21 10:07:29.223 | INFO     | 850bd3374a0442afa85e464f60da59d5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:29.223 | INFO     | 850bd3374a0442afa85e464f60da59d5 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:07:29.250 | INFO     | 850bd3374a0442afa85e464f60da59d5 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents/2f908d7847f511f0919188f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-21 10:07:29.278 | INFO     | 850bd3374a0442afa85e464f60da59d5 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents/2f908d7847f511f0919188f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-21 10:07:29.280 | INFO     | 850bd3374a0442afa85e464f60da59d5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/2f908d7847f511f0919188f4da8e1b91/content | 63.378ms
2025-08-21 10:07:34.731 | INFO     | ef964f172275430ab591055c54821c8e | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:34.732 | INFO     | ef964f172275430ab591055c54821c8e | 成功认证Java用户: pythontest
2025-08-21 10:07:34.747 | INFO     | ef964f172275430ab591055c54821c8e | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-21 10:07:34.748 | INFO     | ef964f172275430ab591055c54821c8e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.840ms
2025-08-21 10:07:34.750 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:34.751 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | 成功认证Java用户: pythontest
2025-08-21 10:07:34.756 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:34.756 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:34.771 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:34.773 | INFO     | a64128bdfa314fe89449e0e6d78f6817 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.602ms
2025-08-21 10:07:34.775 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:34.776 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | 成功认证Java用户: pythontest
2025-08-21 10:07:34.781 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:34.781 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-21 10:07:34.799 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:34.801 | INFO     | 81bc0c903756465e8500ce6c84ace2ba | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.316ms
2025-08-21 10:07:36.468 | INFO     | 2815661f59c0467a9dc35fc6826be88b | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:36.470 | INFO     | 2815661f59c0467a9dc35fc6826be88b | 成功认证Java用户: pythontest
2025-08-21 10:07:36.474 | INFO     | 2815661f59c0467a9dc35fc6826be88b | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:36.474 | INFO     | 2815661f59c0467a9dc35fc6826be88b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:36.486 | INFO     | 2815661f59c0467a9dc35fc6826be88b | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:36.488 | INFO     | 2815661f59c0467a9dc35fc6826be88b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 20.329ms
2025-08-21 10:07:36.496 | INFO     | 2ef359b2a40b4e30b02419faa13687da | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:36.497 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:36.498 | INFO     | 2ef359b2a40b4e30b02419faa13687da | 成功认证Java用户: pythontest
2025-08-21 10:07:36.499 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | 成功认证Java用户: pythontest
2025-08-21 10:07:36.508 | INFO     | 2ef359b2a40b4e30b02419faa13687da | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:36.509 | INFO     | 2ef359b2a40b4e30b02419faa13687da | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:36.513 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:36.513 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:36.538 | INFO     | 2ef359b2a40b4e30b02419faa13687da | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:36.540 | INFO     | 2ef359b2a40b4e30b02419faa13687da | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=20&orderby=create_time&desc=true | 43.583ms
2025-08-21 10:07:36.541 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:36.543 | INFO     | fdf5bbeeaf40416f8e75048e532fae84 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 46.223ms
2025-08-21 10:07:39.329 | INFO     | ab74508702544e00a3d8657791ab7a01 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:39.330 | INFO     | ab74508702544e00a3d8657791ab7a01 | 成功认证Java用户: pythontest
2025-08-21 10:07:39.335 | INFO     | ab74508702544e00a3d8657791ab7a01 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:39.335 | INFO     | ab74508702544e00a3d8657791ab7a01 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:39.350 | INFO     | ab74508702544e00a3d8657791ab7a01 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:39.351 | INFO     | ab74508702544e00a3d8657791ab7a01 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 22.763ms
2025-08-21 10:07:39.358 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:39.359 | INFO     | 416ea3c16da942419f47675d509bd5c8 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:07:39.360 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | 成功认证Java用户: pythontest
2025-08-21 10:07:39.360 | INFO     | 416ea3c16da942419f47675d509bd5c8 | 成功认证Java用户: pythontest
2025-08-21 10:07:39.373 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:39.373 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:39.378 | INFO     | 416ea3c16da942419f47675d509bd5c8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:07:39.378 | INFO     | 416ea3c16da942419f47675d509bd5c8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:07:39.401 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:39.403 | INFO     | 416ea3c16da942419f47675d509bd5c8 | HTTP Request: GET http://*************:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:07:39.406 | INFO     | 80c5265de97d487caa99f8a539eac8b3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=20&orderby=create_time&desc=true | 46.980ms
2025-08-21 10:07:39.407 | INFO     | 416ea3c16da942419f47675d509bd5c8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 47.717ms
2025-08-21 10:07:52.652 | INFO     | ffbfb1c94367447b92c19b16ee3a614b | 127.0.0.1       | GET      | 200    | /docs | 0.817ms
2025-08-21 10:07:54.215 | INFO     | f6e45dfaac0449a780904c9b00db89ef | 127.0.0.1       | GET      | 200    | /openapi | 112.887ms
2025-08-21 10:07:55.295 | INFO     | 54ddcf91dbe642b7a9895afa0b0065e9 | 127.0.0.1       | GET      | 200    | /docs | 1.007ms
2025-08-21 10:07:55.395 | INFO     | e34b73b68295477494ebcb4035cf7762 | 127.0.0.1       | GET      | 200    | /openapi | 3.412ms
2025-08-21 10:07:57.885 | INFO     | ae7a2ab8823a443d92bcb5ebeffa1d53 | 127.0.0.1       | GET      | 200    | /docs | 1.105ms
2025-08-21 10:07:57.983 | INFO     | 9233a50f629e4020a482a745a7ab03e3 | 127.0.0.1       | GET      | 200    | /openapi | 3.011ms
2025-08-21 10:09:49.823 | INFO     | 1777bbafd49d4a549a757dd65393f410 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:09:49.823 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:09:49.825 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:09:49.826 | INFO     | 1777bbafd49d4a549a757dd65393f410 | 成功认证Java用户: pythontest
2025-08-21 10:09:49.826 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | 成功认证Java用户: pythontest
2025-08-21 10:09:49.826 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | 成功认证Java用户: pythontest
2025-08-21 10:09:49.834 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:09:49.834 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-21 10:09:49.837 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:09:49.837 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:09:49.842 | INFO     | 1777bbafd49d4a549a757dd65393f410 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-21 10:09:49.843 | INFO     | 1777bbafd49d4a549a757dd65393f410 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 20.056ms
2025-08-21 10:09:49.852 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:09:49.852 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:09:49.854 | INFO     | c872514b38c14e379497cfdc5d8b1c9c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.159ms
2025-08-21 10:09:49.854 | INFO     | b98f6e8d989c4c558df7e5c0afd80e13 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.128ms
2025-08-21 10:10:01.432 | INFO     | b52c2eb75ff64c2498fe1286d854baac | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:01.432 | INFO     | b52c2eb75ff64c2498fe1286d854baac | 成功认证Java用户: pythontest
2025-08-21 10:10:01.437 | INFO     | b52c2eb75ff64c2498fe1286d854baac | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:01.437 | INFO     | b52c2eb75ff64c2498fe1286d854baac | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:01.438 | INFO     | b52c2eb75ff64c2498fe1286d854baac | 创建知识库请求数据: {'name': 'pythontest5', 'description': '', 'permission': 'team'}
2025-08-21 10:10:01.473 | INFO     | b52c2eb75ff64c2498fe1286d854baac | HTTP Request: POST http://*************:9222/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-21 10:10:01.474 | INFO     | b52c2eb75ff64c2498fe1286d854baac | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 42.261ms
2025-08-21 10:10:01.481 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:01.482 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | 成功认证Java用户: pythontest
2025-08-21 10:10:01.486 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:01.486 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:01.499 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:01.500 | INFO     | aec93c33b05f4bbab9304bc971bea5c1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 19.226ms
2025-08-21 10:10:01.502 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:01.503 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | 成功认证Java用户: pythontest
2025-08-21 10:10:01.507 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:01.508 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-21 10:10:01.521 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:01.522 | INFO     | e1af29e8baaa4e8293d87cc1c1964099 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 20.256ms
2025-08-21 10:10:07.333 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:07.334 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | 成功认证Java用户: pythontest
2025-08-21 10:10:07.339 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:07.339 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:07.349 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:07.350 | INFO     | 7395b5f5065c42fdb0fbf4b6df62b5a7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 16.996ms
2025-08-21 10:10:07.358 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:07.359 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | 成功认证Java用户: pythontest
2025-08-21 10:10:07.364 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:07.364 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:07.377 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:07.378 | INFO     | aad13598d58a48dd8865e6a94f8a0989 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.521ms
2025-08-21 10:10:07.379 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:07.380 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | 成功认证Java用户: pythontest
2025-08-21 10:10:07.385 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:07.385 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:07.398 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:07.399 | INFO     | 1fc47fe85678489bb329dd88ae5a62a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 19.351ms
2025-08-21 10:10:16.888 | INFO     | 0e216e2febd9450c8aad287272f29582 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:16.889 | INFO     | 0e216e2febd9450c8aad287272f29582 | 成功认证Java用户: pythontest
2025-08-21 10:10:16.997 | INFO     | 0e216e2febd9450c8aad287272f29582 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:16.998 | INFO     | 0e216e2febd9450c8aad287272f29582 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:17.136 | INFO     | 0e216e2febd9450c8aad287272f29582 | 文件上传完成: test (1).pptx, 大小: 11122759 bytes
2025-08-21 10:10:17.403 | INFO     | 0e216e2febd9450c8aad287272f29582 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:17.406 | INFO     | 0e216e2febd9450c8aad287272f29582 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 517.967ms
2025-08-21 10:10:17.412 | INFO     | c7395cb3cae5417e9386fd90d166db30 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:17.413 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 成功认证Java用户: pythontest
2025-08-21 10:10:17.419 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:17.420 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:17.420 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 文件上传完成: test.pdf, 大小: 96853 bytes
2025-08-21 10:10:17.664 | INFO     | c7395cb3cae5417e9386fd90d166db30 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:17.665 | INFO     | c7395cb3cae5417e9386fd90d166db30 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fb633dee7e3311f0aa0b88f4da8e1b91', 'location': 'test.pdf', 'name': 'test.pdf', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 96853, 'thumbnail': 'thumbnail_fb633dee7e3311f0aa0b88f4da8e1b91.png', 'type': 'pdf'}]}
2025-08-21 10:10:17.665 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 响应data类型: <class 'list'>
2025-08-21 10:10:17.665 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fb633dee7e3311f0aa0b88f4da8e1b91', 'location': 'test.pdf', 'name': 'test.pdf', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 96853, 'thumbnail': 'thumbnail_fb633dee7e3311f0aa0b88f4da8e1b91.png', 'type': 'pdf'}]
2025-08-21 10:10:17.665 | INFO     | c7395cb3cae5417e9386fd90d166db30 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 253.344ms
2025-08-21 10:10:17.669 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:17.670 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 成功认证Java用户: pythontest
2025-08-21 10:10:17.675 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:17.675 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:17.675 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 文件上传完成: test.docx, 大小: 21409 bytes
2025-08-21 10:10:17.796 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:17.796 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fb903ac27e3311f0a32a88f4da8e1b91', 'location': 'test.docx', 'name': 'test.docx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 21409, 'thumbnail': '', 'type': 'doc'}]}
2025-08-21 10:10:17.796 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 响应data类型: <class 'list'>
2025-08-21 10:10:17.796 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fb903ac27e3311f0a32a88f4da8e1b91', 'location': 'test.docx', 'name': 'test.docx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 21409, 'thumbnail': '', 'type': 'doc'}]
2025-08-21 10:10:17.797 | INFO     | 47c15db31a7d4ee593befb9eebb048f0 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 127.757ms
2025-08-21 10:10:17.802 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:17.802 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 成功认证Java用户: pythontest
2025-08-21 10:10:17.817 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:17.818 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:17.835 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 文件上传完成: test.xlsx, 大小: 1152626 bytes
2025-08-21 10:10:17.966 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:17.966 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fba9ccdf7e3311f09b1288f4da8e1b91', 'location': 'test.xlsx', 'name': 'test.xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]}
2025-08-21 10:10:17.966 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 响应data类型: <class 'list'>
2025-08-21 10:10:17.967 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fba9ccdf7e3311f09b1288f4da8e1b91', 'location': 'test.xlsx', 'name': 'test.xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]
2025-08-21 10:10:17.967 | INFO     | e3dcdd304d1c4c628cc1b4a6d082ba56 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 166.319ms
2025-08-21 10:10:17.971 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:17.972 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 成功认证Java用户: pythontest
2025-08-21 10:10:17.977 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:17.977 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:17.978 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 文件上传完成: 新文件 8 (2).txt, 大小: 338 bytes
2025-08-21 10:10:18.101 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:18.101 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbbf41967e3311f09a8488f4da8e1b91', 'location': '新文件 8 (2).txt', 'name': '新文件 8 (2).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]}
2025-08-21 10:10:18.101 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 响应data类型: <class 'list'>
2025-08-21 10:10:18.101 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbbf41967e3311f09a8488f4da8e1b91', 'location': '新文件 8 (2).txt', 'name': '新文件 8 (2).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]
2025-08-21 10:10:18.102 | INFO     | 4208dd15dc8d4e44b4c1ab93b13b17ca | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 130.689ms
2025-08-21 10:10:18.106 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:18.107 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 成功认证Java用户: pythontest
2025-08-21 10:10:18.112 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:18.112 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:18.112 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 文件上传完成: 新文件 8 (1).txt, 大小: 338 bytes
2025-08-21 10:10:18.241 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:18.242 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbd422eb7e3311f0a5d988f4da8e1b91', 'location': '新文件 8 (1).txt', 'name': '新文件 8 (1).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]}
2025-08-21 10:10:18.242 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 响应data类型: <class 'list'>
2025-08-21 10:10:18.243 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbd422eb7e3311f0a5d988f4da8e1b91', 'location': '新文件 8 (1).txt', 'name': '新文件 8 (1).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]
2025-08-21 10:10:18.243 | INFO     | d3794f8c3cfb47f4b9a4d04e7da18be1 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 137.015ms
2025-08-21 10:10:18.247 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:18.248 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 成功认证Java用户: pythontest
2025-08-21 10:10:18.252 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:18.253 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-21 10:10:18.253 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 文件上传完成: 新文件 8.txt, 大小: 338 bytes
2025-08-21 10:10:18.390 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-21 10:10:18.390 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbeb1a207e3311f0920888f4da8e1b91', 'location': '新文件 8.txt', 'name': '新文件 8.txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]}
2025-08-21 10:10:18.391 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 响应data类型: <class 'list'>
2025-08-21 10:10:18.391 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': 'fbeb1a207e3311f0920888f4da8e1b91', 'location': '新文件 8.txt', 'name': '新文件 8.txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]
2025-08-21 10:10:18.391 | INFO     | 1c733b2ab27448e88e25c43d09ca6c07 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 144.419ms
2025-08-21 10:10:18.410 | INFO     | 684eb7dedf8140718ba1df2008f6712c | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:18.411 | INFO     | 999ed7c275c5445794ccb849ec160726 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:18.412 | INFO     | 684eb7dedf8140718ba1df2008f6712c | 成功认证Java用户: pythontest
2025-08-21 10:10:18.412 | INFO     | 999ed7c275c5445794ccb849ec160726 | 成功认证Java用户: pythontest
2025-08-21 10:10:18.417 | INFO     | 684eb7dedf8140718ba1df2008f6712c | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:18.418 | INFO     | 684eb7dedf8140718ba1df2008f6712c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:18.421 | INFO     | 999ed7c275c5445794ccb849ec160726 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:18.421 | INFO     | 999ed7c275c5445794ccb849ec160726 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:18.451 | INFO     | 684eb7dedf8140718ba1df2008f6712c | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:18.453 | INFO     | 999ed7c275c5445794ccb849ec160726 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:18.454 | INFO     | 684eb7dedf8140718ba1df2008f6712c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 43.962ms
2025-08-21 10:10:18.455 | INFO     | 999ed7c275c5445794ccb849ec160726 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 44.111ms
2025-08-21 10:10:27.735 | INFO     | b19c1824c1944cf18e35a288efafbd1c | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:27.736 | INFO     | b19c1824c1944cf18e35a288efafbd1c | 成功认证Java用户: pythontest
2025-08-21 10:10:27.740 | INFO     | b19c1824c1944cf18e35a288efafbd1c | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:27.740 | INFO     | b19c1824c1944cf18e35a288efafbd1c | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.044 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.044 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.045 | INFO     | db6f879b2bb34cca91f17ed80a083747 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.045 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.045 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.045 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | 成功认证Java用户: pythontest
2025-08-21 10:10:28.047 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | 成功认证Java用户: pythontest
2025-08-21 10:10:28.047 | INFO     | db6f879b2bb34cca91f17ed80a083747 | 成功认证Java用户: pythontest
2025-08-21 10:10:28.048 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | 成功认证Java用户: pythontest
2025-08-21 10:10:28.049 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | 成功认证Java用户: pythontest
2025-08-21 10:10:28.056 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.056 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.057 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.057 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.062 | INFO     | db6f879b2bb34cca91f17ed80a083747 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.062 | INFO     | db6f879b2bb34cca91f17ed80a083747 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.066 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.067 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.069 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.070 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-21 10:10:28.151 | INFO     | b19c1824c1944cf18e35a288efafbd1c | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.153 | INFO     | b19c1824c1944cf18e35a288efafbd1c | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 418.080ms
2025-08-21 10:10:28.178 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.179 | INFO     | 155e07a10d2449e0958f3fa60a2d2813 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbd422eb7e3311f0a5d988f4da8e1b91/parse | 135.187ms
2025-08-21 10:10:28.226 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.226 | INFO     | 1fe9ce1e30764303a1e5371075816e23 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbbf41967e3311f09a8488f4da8e1b91/parse | 182.105ms
2025-08-21 10:10:28.269 | INFO     | db6f879b2bb34cca91f17ed80a083747 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.269 | INFO     | db6f879b2bb34cca91f17ed80a083747 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fba9ccdf7e3311f09b1288f4da8e1b91/parse | 224.261ms
2025-08-21 10:10:28.279 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.280 | INFO     | 0d220b62d4074bb98e92431d9b0ab935 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb903ac27e3311f0a32a88f4da8e1b91/parse | 235.434ms
2025-08-21 10:10:28.282 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | HTTP Request: POST http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-21 10:10:28.283 | INFO     | e4648df2d0a349f1bf05bbdd2ff8482e | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb633dee7e3311f0aa0b88f4da8e1b91/parse | 237.933ms
2025-08-21 10:10:28.287 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:28.288 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | 成功认证Java用户: pythontest
2025-08-21 10:10:28.292 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:28.292 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:28.303 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:28.304 | INFO     | b35726e17cfb4963b2c57f00983d0cf9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 17.690ms
2025-08-21 10:10:38.623 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:38.624 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | 成功认证Java用户: pythontest
2025-08-21 10:10:38.629 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:38.629 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:38.641 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:38.642 | INFO     | 89d1c1e7f0d4429fb7fe880f6ffaadfe | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.261ms
2025-08-21 10:10:48.312 | INFO     | d64d806e21214f7d9d65799e67c770cb | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:10:48.313 | INFO     | d64d806e21214f7d9d65799e67c770cb | 成功认证Java用户: pythontest
2025-08-21 10:10:48.317 | INFO     | d64d806e21214f7d9d65799e67c770cb | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:10:48.317 | INFO     | d64d806e21214f7d9d65799e67c770cb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:10:48.330 | INFO     | d64d806e21214f7d9d65799e67c770cb | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:10:48.331 | INFO     | d64d806e21214f7d9d65799e67c770cb | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.674ms
2025-08-21 10:12:26.185 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:12:26.187 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | 成功认证Java用户: pythontest
2025-08-21 10:12:26.191 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:12:26.192 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 10:12:26.206 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 10:12:26.207 | INFO     | 0acf334c9e8c4d4092fe6d447cf4c617 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 22.722ms
2025-08-21 10:12:27.035 | INFO     | f4a5c04372214d03b3e3baf38a695044 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:12:27.037 | INFO     | f4a5c04372214d03b3e3baf38a695044 | 成功认证Java用户: pythontest
2025-08-21 10:12:27.041 | INFO     | f4a5c04372214d03b3e3baf38a695044 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:12:27.042 | INFO     | f4a5c04372214d03b3e3baf38a695044 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:12:28.823 | INFO     | f4a5c04372214d03b3e3baf38a695044 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 10:12:28.824 | INFO     | f4a5c04372214d03b3e3baf38a695044 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 1789.900ms
2025-08-21 10:12:34.713 | INFO     | 05367089c3cc4c33b48fc09083844c8f | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:12:34.715 | INFO     | 05367089c3cc4c33b48fc09083844c8f | 成功认证Java用户: pythontest
2025-08-21 10:12:34.719 | INFO     | 05367089c3cc4c33b48fc09083844c8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:12:34.720 | INFO     | 05367089c3cc4c33b48fc09083844c8f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:12:34.886 | INFO     | 05367089c3cc4c33b48fc09083844c8f | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb633dee7e3311f0aa0b88f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 10:12:34.889 | INFO     | 05367089c3cc4c33b48fc09083844c8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb633dee7e3311f0aa0b88f4da8e1b91/chunks/page=1&page_size=50 | 175.715ms
2025-08-21 10:19:57.035 | INFO     | b70619319c464d3a9c63405690e87601 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:19:57.037 | INFO     | b70619319c464d3a9c63405690e87601 | 成功认证Java用户: pythontest
2025-08-21 10:19:57.041 | INFO     | b70619319c464d3a9c63405690e87601 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:19:57.042 | INFO     | b70619319c464d3a9c63405690e87601 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:19:57.143 | INFO     | b70619319c464d3a9c63405690e87601 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 10:19:57.145 | INFO     | b70619319c464d3a9c63405690e87601 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 110.936ms
2025-08-21 10:23:32.236 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:23:32.237 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | 成功认证Java用户: pythontest
2025-08-21 10:23:32.241 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:23:32.242 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:23:32.336 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbd422eb7e3311f0a5d988f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 10:23:32.337 | INFO     | 85ae056323cf4e858b121a9171d1ce54 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbd422eb7e3311f0a5d988f4da8e1b91/chunks/page=1&page_size=50 | 101.484ms
2025-08-21 10:42:59.334 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | JWT标准验证成功，获取UUID: 56e325a4-911e-4ab1-9275-4a07d202d576
2025-08-21 10:42:59.337 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | 成功认证Java用户: pythontest
2025-08-21 10:42:59.342 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 10:42:59.342 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 10:42:59.448 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 10:42:59.449 | INFO     | ab393ccb4a514aeb9df32422eee98e6c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 115.447ms
2025-08-21 15:27:57.779 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-21 17:05:29.270 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:05:29.276 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | 成功认证Java用户: pythontest
2025-08-21 17:05:29.320 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:05:29.320 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 17:05:29.361 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | HTTP Request: GET http://*************:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 17:05:29.368 | INFO     | 5865c5997ee4493d895a38ddfe56a2cb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 119.695ms
2025-08-21 17:05:29.380 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:05:29.380 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | 成功认证Java用户: pythontest
2025-08-21 17:05:29.399 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:05:29.399 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 17:05:29.413 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 17:05:29.415 | INFO     | 53af2f218cfb41f4b3d46caf80b67430 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 34.700ms
2025-08-21 17:05:29.417 | INFO     | bc716694581a4f3589e023a404b5d29a | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:05:29.418 | INFO     | bc716694581a4f3589e023a404b5d29a | 成功认证Java用户: pythontest
2025-08-21 17:05:29.422 | INFO     | bc716694581a4f3589e023a404b5d29a | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:05:29.422 | INFO     | bc716694581a4f3589e023a404b5d29a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 17:05:29.434 | INFO     | bc716694581a4f3589e023a404b5d29a | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 17:05:29.435 | INFO     | bc716694581a4f3589e023a404b5d29a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.537ms
2025-08-21 17:05:32.133 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:05:32.135 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | 成功认证Java用户: pythontest
2025-08-21 17:05:32.140 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:05:32.140 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 17:05:32.151 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 17:05:32.152 | INFO     | 850a0d6f953e42c5bfa8b65ca914e541 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.876ms
2025-08-21 17:05:33.181 | INFO     | 961c740c1d914d17814ac2481ec48063 | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:05:33.182 | INFO     | 961c740c1d914d17814ac2481ec48063 | 成功认证Java用户: pythontest
2025-08-21 17:05:33.186 | INFO     | 961c740c1d914d17814ac2481ec48063 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:05:33.186 | INFO     | 961c740c1d914d17814ac2481ec48063 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-21 17:05:33.330 | INFO     | 961c740c1d914d17814ac2481ec48063 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-21 17:05:33.330 | INFO     | 961c740c1d914d17814ac2481ec48063 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 150.037ms
2025-08-21 17:10:24.227 | INFO     | 5c443c207be5483cbb61682518707af4 | JWT标准验证成功，获取UUID: 19dc7da9-6ec9-4f4b-bfea-f23fd8d737b1
2025-08-21 17:10:24.229 | INFO     | 5c443c207be5483cbb61682518707af4 | 成功认证Java用户: pythontest
2025-08-21 17:10:24.233 | INFO     | 5c443c207be5483cbb61682518707af4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=19
2025-08-21 17:10:24.233 | INFO     | 5c443c207be5483cbb61682518707af4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-21 17:10:24.265 | INFO     | 5c443c207be5483cbb61682518707af4 | HTTP Request: GET http://*************:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-21 17:10:24.267 | INFO     | 5c443c207be5483cbb61682518707af4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 39.953ms
